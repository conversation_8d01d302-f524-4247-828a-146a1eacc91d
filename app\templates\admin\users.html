{% extends "base.html" %}

{% block title %}إدارة المستخدمين - CMSVS{% endblock %}

{% block extra_css %}
<!-- Force browser cache refresh -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<style>
    /* Enhanced User Index Design - Based on user_index.html */
    :root {
        --primary-color: #3b82f6;
        --secondary-color: #6b7280;
        --success-color: #10b981;
        --danger-color: #ef4444;
        --warning-color: #f59e0b;
        --info-color: #06b6d4;
    }

    /* High Specificity CSS Reset for Users Page */
    html body .users-page-container * {
        box-sizing: border-box;
    }

    /* Main Header Enhancement */
    html body .users-page-container .main-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%) !important;
        color: white !important;
        padding: 2rem 0 !important;
        margin-bottom: 2rem !important;
        border-radius: 0 0 20px 20px !important;
        box-shadow: 0 6px 15px rgba(0,0,0,0.08) !important;
    }

    /* Statistics Cards */
    html body .users-page-container .stats-card {
        border: none !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
        background: white !important;
        margin-bottom: 1rem !important;
    }

    html body .users-page-container .stats-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
    }

    html body .users-page-container .stats-icon {
        width: 48px !important;
        height: 48px !important;
        border-radius: 12px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 1.5rem !important;
    }

    /* Force override of base.html styles with maximum specificity */
    html body .users-page-container .card,
    html body .users-page-container .card.card,
    html body .users-page-container div.card {
        background: white !important;
        border: none !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        transform: none !important;
        animation: none !important;
        margin-bottom: 1.5rem !important;
        overflow: hidden !important;
        position: relative !important;
        transition: all 0.3s ease !important;
    }

    html body .users-page-container .card:hover,
    html body .users-page-container .card.card:hover,
    html body .users-page-container div.card:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--template-shadow-hover) !important;
    }

    html body .users-page-container .btn,
    html body .users-page-container .btn.btn,
    html body .users-page-container button.btn {
        border-radius: 12px !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        padding: 0.5rem 1.25rem !important;
        transition: all 0.3s ease !important;
        border: 1px solid transparent !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.5rem !important;
        text-transform: none !important;
    }

    html body .users-page-container .btn-primary,
    html body .users-page-container .btn.btn-primary,
    html body .users-page-container button.btn-primary {
        background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%) !important;
        color: white !important;
        border: none !important;
    }

    html body .users-page-container .btn-primary:hover,
    html body .users-page-container .btn.btn-primary:hover,
    html body .users-page-container button.btn-primary:hover {
        background: linear-gradient(135deg, #3d43a8 0%, #7a80db 100%) !important;
        color: white !important;
        transform: translateY(-2px) !important;
    }

    /* Custom Template Design - Users Page */
    :root {
        /* Template Color Scheme */
        --template-primary-start: #4e54c8;
        --template-primary-end: #8f94fb;
        --template-secondary-start: #6a82fb;
        --template-secondary-end: #fc5c7d;
        --template-background: #f8f9fa;
        --template-white: #ffffff;
        --template-dark: #333333;
        --template-muted: #6c757d;
        --template-light-gray: #e9ecef;
        --template-success: #28a745;
        --template-warning: #ffc107;
        --template-danger: #dc3545;
        --template-info: #17a2b8;

        /* Template Shadows */
        --template-shadow: 0 6px 15px rgba(0,0,0,0.08);
        --template-shadow-hover: 0 10px 25px rgba(0,0,0,0.12);

        /* Template Border Radius */
        --template-radius: 15px;
        --template-radius-small: 12px;
        --template-radius-large: 20px;
    }

    /* Page Layout - Override base.html styles */
    body {
        background-color: var(--template-background) !important;
        background: var(--template-background) !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        color: var(--template-dark) !important;
    }

    .container-xxl {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    /* Template Gradient Header with Maximum Specificity */
    html body .users-page-container .gradient-header,
    html body .gradient-header {
        background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%) !important;
        color: white !important;
        padding: 3rem 0 !important;
        margin-bottom: 2rem !important;
        border-radius: 0 0 20px 20px !important;
        box-shadow: var(--template-shadow) !important;
        text-align: center !important;
        position: relative !important;
        z-index: 1 !important;
    }

    html body .users-page-container .gradient-header .display-5,
    html body .gradient-header .display-5 {
        font-size: 2.5rem !important;
        font-weight: 700 !important;
        margin-bottom: 0.5rem !important;
        color: white !important;
    }

    html body .users-page-container .gradient-header .lead,
    html body .gradient-header .lead {
        font-size: 1.125rem !important;
        opacity: 0.9 !important;
        margin-bottom: 0 !important;
        color: white !important;
    }

    html body .users-page-container .gradient-header i,
    html body .gradient-header i {
        font-size: 3rem !important;
        margin-bottom: 1rem !important;
        opacity: 0.8 !important;
        color: white !important;
    }

    /* Template Avatar Styles */
    .avatar {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%);
        color: white;
        font-weight: bold;
        flex-shrink: 0;
        transition: all 0.3s ease;
        margin-left: 10px;
    }

    .avatar:hover {
        transform: scale(1.05);
    }

    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 0.875rem;
    }

    .avatar-xs {
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    /* Template Card Styles - Apply to users page container */
    .users-page-container .card {
        background: var(--template-white) !important;
        border: none !important;
        border-radius: var(--template-radius) !important;
        box-shadow: var(--template-shadow) !important;
        transition: all 0.3s ease !important;
        margin-bottom: 1.5rem !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .users-page-container .card:hover {
        transform: translateY(-5px) !important;
        box-shadow: var(--template-shadow-hover) !important;
    }

    html body .users-page-container .card-header,
    html body .users-page-container .card .card-header,
    html body .users-page-container div.card .card-header {
        background: linear-gradient(135deg, var(--template-secondary-start) 0%, var(--template-secondary-end) 100%) !important;
        border-bottom: none !important;
        border-radius: var(--template-radius) var(--template-radius) 0 0 !important;
        padding: 1.5rem 2rem !important;
        margin-bottom: 0 !important;
        position: relative !important;
        color: white !important;
    }

    .card-body {
        padding: 1.5rem 2rem;
    }

    .card-title {
        margin-bottom: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        color: white;
    }

    .card-subtitle {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
    }

    /* Template Button Styles - Apply to users page container */
    .users-page-container .btn {
        border-radius: var(--template-radius-small) !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        padding: 0.5rem 1.25rem !important;
        transition: all 0.3s ease !important;
        border: 1px solid transparent !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.5rem !important;
        text-transform: none !important;
    }

    .users-page-container .btn:hover {
        transform: translateY(-2px) !important;
    }

    .users-page-container .btn-primary {
        background: linear-gradient(135deg, var(--template-primary-start) 0%, var(--template-primary-end) 100%) !important;
        color: white !important;
        border: none !important;
    }

    .users-page-container .btn-primary:hover {
        background: linear-gradient(135deg, #3d43a8 0%, #7a80db 100%) !important;
        color: white !important;
    }

    .btn-outline-primary {
        color: var(--template-primary-start);
        border: 1px solid var(--template-primary-start);
        background: transparent;
    }

    .btn-outline-primary:hover {
        background: var(--template-primary-start);
        border-color: var(--template-primary-start);
        color: white;
    }

    .btn-outline-secondary {
        color: var(--template-muted);
        border: 1px solid #dee2e6;
        background: transparent;
    }

    .btn-outline-secondary:hover {
        background: var(--template-muted);
        border-color: var(--template-muted);
        color: white;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
        border-radius: var(--template-radius-small);
    }

    .btn-icon {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--template-radius-small);
    }

    /* Custom Action Buttons - Enhanced */
    html body .users-page-container .btn-edit,
    html body .users-page-container a.btn-edit,
    html body .users-page-container .dropdown-item.btn-edit {
        background-color: #4e54c8 !important;
        background: #4e54c8 !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 0.5rem 1rem !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    html body .users-page-container .btn-edit:hover,
    html body .users-page-container a.btn-edit:hover,
    html body .users-page-container .dropdown-item.btn-edit:hover {
        background-color: #3d43a8 !important;
        background: #3d43a8 !important;
        color: white !important;
        transform: translateY(-2px) !important;
        text-decoration: none !important;
    }

    html body .users-page-container .btn-delete,
    html body .users-page-container button.btn-delete,
    html body .users-page-container .dropdown-item.btn-delete {
        background-color: #fc5c7d !important;
        background: #fc5c7d !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 0.5rem 1rem !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    html body .users-page-container .btn-delete:hover,
    html body .users-page-container button.btn-delete:hover,
    html body .users-page-container .dropdown-item.btn-delete:hover {
        background-color: #e04a6b !important;
        background: #e04a6b !important;
        color: white !important;
        transform: translateY(-2px) !important;
    }

    .hide-arrow::after {
        display: none !important;
    }

    /* Template Form Controls */
    .form-control, .form-select {
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius-small);
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        color: var(--template-dark);
        background-color: var(--template-white);
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--template-primary-start);
        box-shadow: 0 0 0 0.2rem rgba(78, 84, 200, 0.25);
        outline: 0;
    }

    .form-label {
        font-weight: 500;
        color: var(--template-dark);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .input-group-text {
        background: var(--template-white);
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius-small);
        color: var(--template-muted);
        font-weight: 400;
    }

    /* Template Dropdown Styles - Enhanced */
    html body .users-page-container .dropdown-menu,
    html body .users-page-container ul.dropdown-menu {
        border: 1px solid #dee2e6 !important;
        border-radius: 15px !important;
        box-shadow: 0 6px 15px rgba(0,0,0,0.08) !important;
        padding: 0.5rem !important;
        margin-top: 0.25rem !important;
        min-width: 180px !important;
        background: var(--template-white) !important;
        z-index: 1050 !important;
    }

    html body .users-page-container .dropdown-item,
    html body .users-page-container .dropdown-menu .dropdown-item,
    html body .users-page-container ul.dropdown-menu li .dropdown-item {
        border-radius: 12px !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
        color: #333333 !important;
        transition: all 0.3s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        border: none !important;
        background: none !important;
        width: 100% !important;
        text-align: right !important;
        font-weight: 400 !important;
        margin-bottom: 0.125rem !important;
        text-decoration: none !important;
    }

    html body .users-page-container .dropdown-item:hover,
    html body .users-page-container .dropdown-menu .dropdown-item:hover,
    html body .users-page-container ul.dropdown-menu li .dropdown-item:hover {
        background-color: rgba(78, 84, 200, 0.1) !important;
        color: #4e54c8 !important;
        text-decoration: none !important;
        transform: translateX(3px) !important;
    }

    html body .users-page-container .dropdown-item i,
    html body .users-page-container .dropdown-menu .dropdown-item i {
        width: 1rem !important;
        text-align: center !important;
        font-size: 0.875rem !important;
        transition: all 0.3s ease !important;
        flex-shrink: 0 !important;
    }

    html body .users-page-container .dropdown-item:hover i,
    html body .users-page-container .dropdown-menu .dropdown-item:hover i {
        color: #4e54c8 !important;
    }

    html body .users-page-container .dropdown-divider,
    html body .users-page-container .dropdown-menu .dropdown-divider {
        margin: 0.5rem 0 !important;
        border-color: #dee2e6 !important;
        opacity: 1 !important;
    }

    /* Professional Action Dropdown Button */
    html body .users-page-container .dropdown-toggle,
    html body .users-page-container button.dropdown-toggle,
    html body .users-page-container .btn-outline-secondary.dropdown-toggle {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 12px !important;
        color: #4e54c8 !important;
        padding: 0.5rem 0.75rem !important;
        transition: all 0.3s ease !important;
        width: auto !important;
        min-width: 40px !important;
        height: 40px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    html body .users-page-container .dropdown-toggle::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%) !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
        z-index: -1 !important;
    }

    html body .users-page-container .dropdown-toggle:hover,
    html body .users-page-container button.dropdown-toggle:hover,
    html body .users-page-container .btn-outline-secondary.dropdown-toggle:hover {
        background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%) !important;
        border-color: #4e54c8 !important;
        color: #ffffff !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 12px rgba(78, 84, 200, 0.25) !important;
    }

    html body .users-page-container .dropdown-toggle:hover::before {
        opacity: 1 !important;
    }

    html body .users-page-container .dropdown-toggle:active,
    html body .users-page-container button.dropdown-toggle:active {
        transform: translateY(0) !important;
        box-shadow: 0 2px 4px rgba(78, 84, 200, 0.15) !important;
    }

    /* Action Button Icon Styling */
    html body .users-page-container .dropdown-toggle i,
    html body .users-page-container button.dropdown-toggle i {
        font-size: 1rem !important;
        transition: all 0.3s ease !important;
    }

    html body .users-page-container .dropdown-toggle:hover i,
    html body .users-page-container button.dropdown-toggle:hover i {
        transform: scale(1.1) !important;
    }

    /* Template Badge Styles - Enhanced Readability */
    html body .users-page-container .badge,
    html body .users-page-container span.badge {
        font-size: 0.75rem !important;
        font-weight: 600 !important;
        padding: 0.5rem 0.875rem !important;
        border-radius: 12px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.375rem !important;
        text-align: center !important;
        white-space: nowrap !important;
        transition: all 0.3s ease !important;
        border: none !important;
        text-transform: none !important;
        letter-spacing: 0.25px !important;
        line-height: 1.2 !important;
    }

    html body .users-page-container .badge:hover,
    html body .users-page-container span.badge:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
    }

    /* Role Badges - High Contrast */
    html body .users-page-container .badge-primary,
    html body .users-page-container span.badge-primary {
        background-color: #4e54c8 !important;
        background: #4e54c8 !important;
        color: #ffffff !important;
        border: none !important;
    }

    html body .users-page-container .badge-warning,
    html body .users-page-container span.badge-warning {
        background-color: #ffc107 !important;
        background: #ffc107 !important;
        color: #333333 !important;
        border: none !important;
    }

    html body .users-page-container .badge-info,
    html body .users-page-container span.badge-info {
        background-color: #17a2b8 !important;
        background: #17a2b8 !important;
        color: #ffffff !important;
        border: none !important;
    }

    /* Status Badges - High Contrast */
    html body .users-page-container .badge-success,
    html body .users-page-container span.badge-success {
        background-color: #28a745 !important;
        background: #28a745 !important;
        color: #ffffff !important;
        border: none !important;
    }

    html body .users-page-container .badge-secondary,
    html body .users-page-container span.badge-secondary {
        background-color: #6c757d !important;
        background: #6c757d !important;
        color: #ffffff !important;
        border: none !important;
    }

    /* User Avatar Styles */
    html body .users-page-container .user-avatar {
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-weight: bold !important;
        color: white !important;
        font-size: 0.875rem !important;
        margin-left: 0.75rem !important;
    }

    /* Role Badge Styles */
    html body .users-page-container .role-badge {
        font-size: 0.75rem !important;
        padding: 0.375rem 0.75rem !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
    }

    /* Status Badge Styles */
    html body .users-page-container .status-badge {
        font-size: 0.75rem !important;
        padding: 0.375rem 0.75rem !important;
        border-radius: 20px !important;
        font-weight: 500 !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 0.25rem !important;
    }

    /* Search Box Enhancement */
    html body .users-page-container .search-box {
        position: relative !important;
    }

    html body .users-page-container .search-box .bi-search {
        position: absolute !important;
        right: 12px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6b7280 !important;
    }

    html body .users-page-container .search-box input {
        padding-right: 2.5rem !important;
    }

    html body .users-page-container .badge-light-secondary,
    html body .users-page-container span.badge-light-secondary {
        background-color: #e9ecef !important;
        background: #e9ecef !important;
        color: #333333 !important;
        border: none !important;
    }

    /* Template Table Styles - Enhanced */
    html body .users-page-container .table-container {
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        overflow: hidden !important;
    }

    html body .users-page-container .users-table-container,
    html body .users-page-container div.users-table-container {
        width: 100% !important;
        overflow: hidden !important;
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        margin: 0 !important;
        position: relative !important;
    }

    html body .users-page-container .users-table,
    html body .users-page-container table.users-table {
        width: 100% !important;
        table-layout: fixed !important;
        margin-bottom: 0 !important;
        border-collapse: separate !important;
        border-spacing: 0 !important;
        background: var(--template-white) !important;
        border: none !important;
    }

    /* Role Colors */
    html body .users-page-container .role-admin { background-color: #fef2f2 !important; color: #dc2626 !important; }
    html body .users-page-container .role-manager { background-color: #faf5ff !important; color: #9333ea !important; }
    html body .users-page-container .role-developer { background-color: #eff6ff !important; color: #2563eb !important; }
    html body .users-page-container .role-designer { background-color: #f0fdf4 !important; color: #16a34a !important; }
    html body .users-page-container .role-analyst { background-color: #fffbeb !important; color: #d97706 !important; }
    html body .users-page-container .role-editor { background-color: #fff7ed !important; color: #ea580c !important; }
    html body .users-page-container .role-user { background-color: #f9fafb !important; color: #6b7280 !important; }

    /* Status Colors */
    html body .users-page-container .status-active { background-color: #f0fdf4 !important; color: #16a34a !important; }
    html body .users-page-container .status-inactive { background-color: #f9fafb !important; color: #6b7280 !important; }
    html body .users-page-container .status-suspended { background-color: #fef2f2 !important; color: #dc2626 !important; }

    /* Avatar Colors */
    html body .users-page-container .avatar-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
    html body .users-page-container .avatar-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important; }
    html body .users-page-container .avatar-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important; }
    html body .users-page-container .avatar-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important; }
    html body .users-page-container .avatar-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important; }
    html body .users-page-container .avatar-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important; }
    html body .users-page-container .avatar-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important; }
    html body .users-page-container .avatar-8 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important; }
    html body .users-page-container .avatar-9 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%) !important; }

    /* Action Button Enhancement */
    html body .users-page-container .btn-action {
        border: none !important;
        background: none !important;
        color: #6b7280 !important;
        padding: 0.5rem !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;
    }

    html body .users-page-container .btn-action:hover {
        background-color: #f3f4f6 !important;
        color: #374151 !important;
    }

    /* Table Header Styles - Maximum Specificity */
    html body .users-page-container .users-table th,
    html body .users-page-container table.users-table th,
    html body .users-page-container .table thead th,
    html body .users-page-container table.table thead th,
    html body .users-page-container thead th {
        background-color: #f8fafc !important;
        background: #f8fafc !important;
        border-bottom: 2px solid #e5e7eb !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
        padding: 1rem 0.75rem !important;
        font-weight: 600 !important;
        color: #374151 !important;
        font-size: 0.8125rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        vertical-align: middle !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        position: relative !important;
        height: auto !important;
        line-height: 1.4 !important;
    }

    /* Table Header Corner Radius */
    html body .users-page-container .users-table th:first-child,
    html body .users-page-container table.users-table th:first-child {
        padding-left: 1.5rem !important;
        border-radius: 15px 0 0 0 !important;
    }

    html body .users-page-container .users-table th:last-child,
    html body .users-page-container table.users-table th:last-child {
        padding-right: 1.5rem !important;
        border-radius: 0 15px 0 0 !important;
    }

    /* Table Cell Styles */
    html body .users-page-container .users-table td,
    html body .users-page-container table.users-table td,
    html body .users-page-container .table tbody td,
    html body .users-page-container table.table tbody td {
        border-bottom: 1px solid #f3f4f6 !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
        padding: 1rem 0.75rem !important;
        vertical-align: middle !important;
        color: #333333 !important;
        font-size: 0.875rem !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        background: white !important;
    }

    html body .users-page-container .users-table td:first-child,
    html body .users-page-container table.users-table td:first-child {
        padding-left: 1.5rem !important;
    }

    html body .users-page-container .users-table td:last-child,
    html body .users-page-container table.users-table td:last-child {
        padding-right: 1.5rem !important;
    }

    /* Table Row Styles */
    html body .users-page-container .users-table tbody tr,
    html body .users-page-container table.users-table tbody tr,
    html body .users-page-container .table tbody tr,
    html body .users-page-container table.table tbody tr {
        transition: all 0.3s ease !important;
        background: var(--template-white) !important;
        border: none !important;
    }

    html body .users-page-container .users-table tbody tr:hover,
    html body .users-page-container table.users-table tbody tr:hover,
    html body .users-page-container .table tbody tr:hover,
    html body .users-page-container table.table tbody tr:hover {
        background-color: #f9fafb !important;
        background: #f9fafb !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* Table Bottom Corner Radius */
    html body .users-page-container .users-table tbody tr:last-child td:first-child,
    html body .users-page-container table.users-table tbody tr:last-child td:first-child {
        border-radius: 0 0 0 15px !important;
    }

    html body .users-page-container .users-table tbody tr:last-child td:last-child,
    html body .users-page-container table.users-table tbody tr:last-child td:last-child {
        border-radius: 0 0 15px 0 !important;
    }

    /* Responsive Column Widths - Enhanced */
    html body .users-page-container .col-checkbox { width: 5% !important; min-width: 50px !important; }
    html body .users-page-container .col-username { width: 25% !important; min-width: 200px !important; }
    html body .users-page-container .col-email { width: 30% !important; min-width: 200px !important; }
    html body .users-page-container .col-role { width: 15% !important; min-width: 100px !important; }
    html body .users-page-container .col-status { width: 15% !important; min-width: 100px !important; }
    html body .users-page-container .col-actions { width: 10% !important; min-width: 80px !important; }

    /* Template User Info Container */
    .user-info-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        min-width: 0;
        flex: 1;
    }

    .user-info-text {
        min-width: 0;
        flex: 1;
    }

    .user-info-text .username {
        font-weight: 600;
        color: var(--template-dark);
        font-size: 0.875rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 0.125rem;
        transition: color 0.3s ease;
    }

    .user-info-text .user-email {
        font-weight: 400;
        color: var(--template-muted);
        font-size: 0.8125rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .user-info-text .current-user-badge {
        font-size: 0.6875rem;
        color: var(--template-primary-start);
        font-weight: 500;
    }

    /* Template Action Buttons */
    .action-buttons-container {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
    }

    .btn-action {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        border-radius: var(--template-radius-small);
        min-width: auto;
        white-space: nowrap;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-action i {
        font-size: 0.875rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
    }

    /* Enhanced Soft UI Dropdown Styles */
    .dropdown-menu {
        border: none;
        border-radius: var(--soft-border-radius-lg);
        box-shadow: var(--soft-shadow-lg);
        z-index: 1050;
        min-width: 200px;
        padding: 0.75rem;
        background: var(--soft-white);
        backdrop-filter: blur(10px);
    }

    .dropdown-item {
        border-radius: var(--soft-border-radius);
        margin: 0.25rem 0;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        color: var(--soft-text-heading);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
        border: 1px solid transparent;
    }

    .dropdown-item:hover {
        background: linear-gradient(135deg, var(--soft-primary-light) 0%, rgba(94, 114, 228, 0.08) 100%);
        color: var(--soft-primary);
        transform: translateX(-3px);
        border-color: rgba(94, 114, 228, 0.2);
        box-shadow: 0 0.125rem 0.25rem rgba(94, 114, 228, 0.15);
    }

    .dropdown-item i {
        width: 1.25rem;
        text-align: center;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover i {
        color: var(--soft-primary);
        transform: scale(1.1);
    }

    /* Content Styling */
    .truncate-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: help;
    }

    /* User Info Styles */
    .user-info-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .avatar {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .avatar-sm {
        width: 38px;
        height: 38px;
        font-size: 0.875rem;
    }

    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .user-info-text {
        flex: 1;
        min-width: 0;
    }

    .username {
        font-weight: 600;
        color: var(--template-dark);
        font-size: 0.875rem;
        line-height: 1.2;
        margin-bottom: 0.125rem;
        transition: color 0.3s ease;
    }

    .user-email {
        color: var(--template-muted);
        font-size: 0.8125rem;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 400;
    }

    .current-user-badge {
        font-size: 0.6875rem;
        color: var(--template-primary-start);
        font-weight: 500;
        margin-top: 0.125rem;
    }

    .fullname-text, .email-text {
        color: var(--template-dark);
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .email-text {
        font-size: 0.8125rem;
        color: var(--template-muted);
        font-weight: 400;
    }

    .email-text:hover {
        color: var(--template-primary-start);
    }

    .fullname-text {
        font-size: 0.875rem;
        color: var(--template-dark);
        font-weight: 500;
    }

    /* Template Form Check Styles */
    .form-check-input {
        width: 1.125rem;
        height: 1.125rem;
        border: 1px solid #dee2e6;
        border-radius: var(--template-radius-small);
        background-color: var(--template-white);
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: var(--template-primary-start);
        border-color: var(--template-primary-start);
    }

    .form-check-input:focus {
        border-color: var(--template-primary-start);
        box-shadow: 0 0 0 0.2rem rgba(78, 84, 200, 0.25);
    }

    .form-check-input:hover {
        border-color: var(--template-primary-start);
    }

    /* Responsive Design - No Horizontal Scroll */

    /* Large Desktop (1200px+) - Enhanced */
    @media (min-width: 1200px) {
        html body .users-page-container .col-checkbox { width: 5% !important; }
        html body .users-page-container .col-username { width: 25% !important; }
        html body .users-page-container .col-email { width: 30% !important; }
        html body .users-page-container .col-role { width: 15% !important; }
        html body .users-page-container .col-status { width: 15% !important; }
        html body .users-page-container .col-actions { width: 10% !important; }

        html body .users-page-container .users-table th,
        html body .users-page-container .users-table td {
            padding: 1rem 0.75rem !important;
            font-size: 0.875rem !important;
        }

        .btn-action .btn-text { display: inline; }
    }

    /* Desktop (992px - 1199px) - Enhanced */
    @media (max-width: 1199px) and (min-width: 992px) {
        html body .users-page-container .col-checkbox { width: 6% !important; }
        html body .users-page-container .col-username { width: 28% !important; }
        html body .users-page-container .col-email { width: 32% !important; }
        html body .users-page-container .col-role { width: 12% !important; }
        html body .users-page-container .col-status { width: 12% !important; }
        html body .users-page-container .col-actions { width: 10% !important; }

        html body .users-page-container .users-table th,
        html body .users-page-container .users-table td {
            padding: 0.8rem 0.6rem !important;
            font-size: 0.8rem !important;
        }

        .btn-action .btn-text { display: none; }
        .btn-action { padding: 0.35rem 0.5rem; }
    }

    /* Tablet (768px - 991px) */
    @media (max-width: 991px) and (min-width: 768px) {
        .col-checkbox { width: 5%; }
        .col-id { width: 7%; }
        .col-username { width: 18%; }
        .col-fullname { width: 0%; } /* Hide on tablet */
        .col-email { width: 25%; }
        .col-role { width: 12%; }
        .col-status { width: 11%; }
        .col-date { width: 12%; }
        .col-actions { width: 10%; }

        .hide-tablet { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.5rem 0.3rem;
            font-size: 0.75rem;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 12px;
        }

        .btn-action {
            padding: 0.3rem 0.4rem;
            font-size: 0.65rem;
        }

        .btn-action .btn-text { display: none; }

        .badge {
            font-size: 0.65rem;
            padding: 0.25rem 0.5rem;
        }
    }

    /* Mobile (576px - 767px) */
    @media (max-width: 767px) and (min-width: 576px) {
        .col-checkbox { width: 6%; }
        .col-id { width: 8%; }
        .col-username { width: 22%; }
        .col-fullname { width: 0%; } /* Hide on mobile */
        .col-email { width: 0%; } /* Hide on mobile */
        .col-role { width: 18%; }
        .col-status { width: 16%; }
        .col-date { width: 15%; }
        .col-actions { width: 15%; }

        .hide-mobile { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.4rem 0.2rem;
            font-size: 0.7rem;
        }

        .avatar-sm {
            width: 28px;
            height: 28px;
            font-size: 11px;
        }

        .btn-action {
            padding: 0.25rem 0.3rem;
            font-size: 0.6rem;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 0.15rem;
        }

        .badge {
            font-size: 0.6rem;
            padding: 0.2rem 0.4rem;
        }

        /* Bulk operations responsive */
        .row.g-3 .col-md-4 {
            margin-bottom: 0.75rem;
        }
    }

    /* Small Mobile (<576px) */
    @media (max-width: 575px) {
        .col-checkbox { width: 8%; }
        .col-id { width: 10%; }
        .col-username { width: 30%; }
        .col-fullname { width: 0%; } /* Hide */
        .col-email { width: 0%; } /* Hide */
        .col-role { width: 22%; }
        .col-status { width: 0%; } /* Hide */
        .col-date { width: 0%; } /* Hide */
        .col-actions { width: 30%; }

        .hide-small-mobile { display: none !important; }

        .users-table th,
        .users-table td {
            padding: 0.3rem 0.15rem;
            font-size: 0.65rem;
        }

        .avatar-sm {
            width: 24px;
            height: 24px;
            font-size: 10px;
        }

        .btn-action {
            padding: 0.2rem 0.25rem;
            font-size: 0.55rem;
            margin: 0.05rem;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 0.1rem;
        }

        .badge {
            font-size: 0.55rem;
            padding: 0.15rem 0.3rem;
        }

        /* Page header responsive */
        .page-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .btn-primary {
            width: 100%;
            font-size: 0.8rem;
        }

        .card-header h5 {
            font-size: 0.85rem;
        }
    }

    /* Loading Animation */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .bi-hourglass-split {
        animation: spin 1s linear infinite;
    }

    /* Checkbox Styling */
    .form-check-input:checked {
        background-color: #7928ca;
        border-color: #7928ca;
    }

    .form-check-input:focus {
        border-color: #7928ca;
        box-shadow: 0 0 0 0.25rem rgba(121, 40, 202, 0.25);
    }

    /* Content Protection and Visibility */
    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Z-index management */
    .dropdown-menu {
        z-index: 1055 !important;
    }

    .card {
        position: relative;
        z-index: 1;
    }

    .table-responsive {
        position: relative;
        z-index: 2;
    }

    /* Prevent content overlap */
    .card-body {
        overflow: visible;
    }

    .table-container {
        margin-top: 0;
        clear: both;
    }

    /* Button text visibility on small screens */
    @media (max-width: 991px) {
        .btn-sm .d-none.d-lg-inline {
            display: none !important;
        }

        .btn-sm {
            min-width: 40px;
            padding: 0.375rem 0.5rem;
        }
    }

    /* Ensure dropdown menus don't get cut off */
    .btn-group .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        left: auto;
        transform: none;
    }

    /* RTL dropdown positioning */
    [dir="rtl"] .dropdown-menu-end {
        right: auto;
        left: 0;
    }

    /* Table cell content protection */
    .table td {
        position: relative;
        overflow: visible;
    }

    /* Bulk operations spacing */
    .bulk-operations-card {
        margin-bottom: 1.5rem;
    }

    /* Page header spacing */
    .page-header {
        margin-bottom: 2rem;
    }

    /* Template Animations and Enhancements */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Enhanced hover effects */
    .users-table tbody tr:hover .avatar {
        transform: scale(1.05);
    }

    .users-table tbody tr:hover .username {
        color: var(--template-primary-start);
    }

    /* Loading state for bulk actions */
    .btn[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    /* Enhanced search input styling */
    .input-group .form-control:focus + .input-group-text,
    .input-group .form-control:focus ~ .input-group-text {
        border-color: var(--template-primary-start);
        background-color: rgba(78, 84, 200, 0.1);
    }

    /* RTL specific enhancements */
    [dir="rtl"] .dropdown-item:hover {
        transform: translateX(3px);
    }

    /* Content styling */
    .truncate-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: help;
    }

    .truncate-text:hover {
        color: var(--template-primary-start);
    }
</style>
{% endblock %}

{% block content %}
<div class="users-page-container">
<!-- Enhanced Header -->
<header class="main-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">قائمة المستخدمين</h1>
                <p class="mb-0 opacity-75">إدارة وتتبع جميع المستخدمين في النظام</p>
            </div>
            <div class="col-md-4 text-md-start text-center mt-3 mt-md-0">
                <a href="/admin/users/new" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مستخدم جديد
                </a>
            </div>
        </div>
    </div>
</header>

<div class="container">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">إجمالي المستخدمين</h6>
                            <h2 class="card-title mb-0">{{ users|length }}</h2>
                        </div>
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                            <i class="bi bi-people"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">المستخدمون النشطون</h6>
                            <h2 class="card-title mb-0 text-success">{{ users|selectattr("is_active")|list|length }}</h2>
                        </div>
                        <div class="stats-icon bg-success bg-opacity-10 text-success">
                            <i class="bi bi-person-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">المستخدمون غير النشطون</h6>
                            <h2 class="card-title mb-0 text-secondary">{{ users|rejectattr("is_active")|list|length }}</h2>
                        </div>
                        <div class="stats-icon bg-secondary bg-opacity-10 text-secondary">
                            <i class="bi bi-person-x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-subtitle mb-2 text-muted">المديرون</h6>
                            <h2 class="card-title mb-0 text-warning">{{ users|selectattr("role.value", "equalto", "admin")|list|length }}</h2>
                        </div>
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                            <i class="bi bi-person-badge"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% if users %}
    <!-- Filters Section -->
    <div class="filter-section">
        <div class="row g-3">
            <div class="col-lg-6">
                <div class="search-box">
                    <input type="text" class="form-control form-control-lg" id="searchInput" placeholder="البحث عن المستخدمين...">
                    <i class="bi bi-search"></i>
                </div>
            </div>
            <div class="col-lg-3">
                <select class="form-select form-select-lg" id="roleFilter">
                    <option value="">جميع الأدوار</option>
                    <option value="admin">مدير النظام</option>
                    <option value="user">مستخدم عادي</option>
                </select>
            </div>
            <div class="col-lg-3">
                <select class="form-select form-select-lg" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
        </div>

        <!-- Bulk Actions Form -->
        <form method="post" action="/admin/users/bulk-action" id="bulkUserForm" class="mt-4">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label class="form-label small fw-medium">العمليات المجمعة</label>
                    <select class="form-select" name="action">
                        <option value="">اختر العملية</option>
                        <option value="activate">تفعيل</option>
                        <option value="deactivate">إلغاء تفعيل</option>
                        <option value="make_admin">ترقية إلى مدير</option>
                        <option value="make_user">تحويل إلى مستخدم</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center gap-2">
                        <span class="badge badge-light-secondary">
                            <span id="selectedCount">0</span> محدد
                        </span>
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100" id="bulkActionBtn" disabled>
                        <i class="bi bi-check-circle me-1"></i>
                        تطبيق العملية
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="table-container">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="usersTable">
                <thead>
                    <tr>
                        <th>
                            <div class="form-check d-flex justify-content-center mb-0">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </div>
                        </th>
                        <th>المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>آخر نشاط</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    {% set avatar_class = "avatar-" ~ ((loop.index % 9) + 1) %}
                    <tr data-name="{{ user.username }}" data-email="{{ user.email }}" data-role="{% if user.role.value == 'admin' %}مدير النظام{% else %}مستخدم{% endif %}" data-status="{% if user.is_active %}نشط{% else %}غير نشط{% endif %}">
                        <td>
                            {% if user.id != current_user.id %}
                            <div class="form-check d-flex justify-content-center mb-0">
                                <input type="checkbox" name="user_ids" value="{{ user.id }}"
                                       class="form-check-input user-checkbox" form="bulkUserForm">
                            </div>
                            {% else %}
                            <div class="text-center">
                                <i class="bi bi-person-badge text-primary" title="حسابك الحالي"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="user-avatar {{ avatar_class }} me-3">{{ user.username[:2].upper() }}</div>
                                <div>
                                    <div class="fw-semibold">{{ user.username }}</div>
                                    <small class="text-muted">ID: {{ user.id }}</small>
                                    {% if user.id == current_user.id %}
                                    <br><span class="badge badge-primary" style="font-size: 0.6875rem; padding: 0.125rem 0.375rem;">أنت</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope me-2 text-muted"></i>
                                {{ user.email }}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-telephone me-2 text-muted"></i>
                                {% if user.full_name %}{{ user.full_name }}{% else %}غير محدد{% endif %}
                            </div>
                        </td>
                        <td>
                            {% if user.role.value == 'admin' %}
                            <span class="role-badge role-admin">مدير النظام</span>
                            {% else %}
                            <span class="role-badge role-user">مستخدم</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                            <span class="status-badge status-active">
                                <i class="bi bi-check-circle"></i>
                                نشط
                            </span>
                            {% else %}
                            <span class="status-badge status-inactive">
                                <i class="bi bi-x-circle"></i>
                                غير نشط
                            </span>
                            {% endif %}
                        </td>
                        <td><span class="text-muted">{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}</span></td>
                        <td>
                            {% if user.id != current_user.id %}
                            <div class="dropdown">
                                <button class="btn-action" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/admin/users/{{ user.id }}/edit"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/toggle-status" class="d-inline w-100">
                                            {% if user.is_active %}
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا المستخدم؟')">
                                                <i class="bi bi-pause-circle me-2"></i>إيقاف المستخدم
                                            </button>
                                            {% else %}
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle me-2"></i>تفعيل المستخدم
                                            </button>
                                            {% endif %}
                                        </form>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    {% if user.role.value != 'user' %}
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline w-100">
                                            <input type="hidden" name="role" value="user">
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مستخدم عادي؟')">
                                                <i class="bi bi-person me-2"></i>مستخدم عادي
                                            </button>
                                        </form>
                                    </li>
                                    {% endif %}
                                    {% if user.role.value != 'admin' %}
                                    <li>
                                        <form method="post" action="/admin/users/{{ user.id }}/update-role" class="d-inline w-100">
                                            <input type="hidden" name="role" value="admin">
                                            <button type="submit" class="dropdown-item"
                                                    onclick="return confirm('هل أنت متأكد من تغيير دور هذا المستخدم إلى مدير نظام؟')">
                                                <i class="bi bi-crown me-2"></i>مدير النظام
                                            </button>
                                        </form>
                                    </li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                </ul>
                            </div>
                            {% else %}
                            <span class="badge badge-primary">أنت</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="text-center mt-4 mb-5">
        <p class="text-muted" id="resultsCount">عرض {{ users|length }} من أصل {{ users|length }} مستخدم</p>
    </div>

{% else %}
    <!-- Empty State -->
    <div class="card">
        <div class="card-body text-center" style="padding: 3rem 1.5rem;">
            <div class="avatar avatar-lg mx-auto mb-4" style="background: linear-gradient(135deg, var(--template-light-gray) 0%, #f8f9fa 100%); color: var(--template-muted); box-shadow: var(--template-shadow);">
                <i class="bi bi-people"></i>
            </div>
            <h5 class="fw-semibold mb-2" style="color: var(--template-dark);">لا يوجد مستخدمون</h5>
            <p class="mb-4" style="color: var(--template-muted);">لم يتم العثور على أي مستخدمين في النظام</p>
            <a href="/admin/users/new" class="btn btn-primary">
                <i class="bi bi-person-plus"></i>
                إنشاء أول مستخدم
            </a>
        </div>
    </div>
{% endif %}

</div> <!-- Close container -->

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and Filter functionality
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const statusFilter = document.getElementById('statusFilter');
    const tableRows = document.querySelectorAll('#usersTable tbody tr');
    const resultsCount = document.getElementById('resultsCount');
    const totalUsers = tableRows.length;

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedRole = roleFilter.value;
        const selectedStatus = statusFilter.value;
        let visibleCount = 0;

        tableRows.forEach(row => {
            const name = row.dataset.name.toLowerCase();
            const email = row.dataset.email.toLowerCase();
            const role = row.dataset.role;
            const status = row.dataset.status;

            const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
            const matchesRole = !selectedRole || role.includes(selectedRole) ||
                               (selectedRole === 'admin' && role.includes('مدير')) ||
                               (selectedRole === 'user' && role.includes('مستخدم'));
            const matchesStatus = !selectedStatus ||
                                 (selectedStatus === 'active' && status === 'نشط') ||
                                 (selectedStatus === 'inactive' && status === 'غير نشط');

            if (matchesSearch && matchesRole && matchesStatus) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        resultsCount.textContent = `عرض ${visibleCount} من أصل ${totalUsers} مستخدم`;
    }

    // Event listeners for search and filter
    if (searchInput) searchInput.addEventListener('input', filterTable);
    if (roleFilter) roleFilter.addEventListener('change', filterTable);
    if (statusFilter) statusFilter.addEventListener('change', filterTable);

    // Bulk operations functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const selectedCountTextSpan = document.getElementById('selectedCountText');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const bulkForm = document.getElementById('bulkUserForm');

    // Select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
    }

    // Individual checkbox change
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedCount();

            // Update select all checkbox state
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
                const totalCheckboxes = userCheckboxes.length;

                selectAllCheckbox.checked = checkedCount === totalCheckboxes;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCheckboxes;
            }
        });
    });

    // Update selected count and button state
    function updateSelectedCount() {
        const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;

        // Update count displays
        if (selectedCountSpan) selectedCountSpan.textContent = checkedCount;
        if (selectedCountTextSpan) selectedCountTextSpan.textContent = checkedCount;

        // Update button state
        if (bulkActionBtn) {
            bulkActionBtn.disabled = checkedCount === 0;

            // Update button text based on selection
            if (checkedCount === 0) {
                bulkActionBtn.innerHTML = '<i class="bi bi-play-circle me-2"></i>تطبيق على المحدد';
            } else {
                bulkActionBtn.innerHTML = `<i class="bi bi-play-circle me-2"></i>تطبيق على ${checkedCount} مستخدم`;
            }
        }
    }

    // Form submission confirmation
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            const checkedCount = document.querySelectorAll('.user-checkbox:checked').length;
            const actionSelect = this.querySelector('select[name="action"]');
            const action = actionSelect.value;

            if (checkedCount === 0) {
                e.preventDefault();
                alert('يرجى اختيار مستخدم واحد على الأقل');
                return;
            }

            if (!action) {
                e.preventDefault();
                alert('يرجى اختيار العملية المطلوبة');
                actionSelect.focus();
                return;
            }

            const actionNames = {
                'activate': 'تفعيل',
                'deactivate': 'إلغاء تفعيل',
                'make_admin': 'ترقية إلى مدير نظام',
                'make_user': 'تحويل إلى مستخدم عادي'
            };

            const actionName = actionNames[action] || action;
            const confirmMessage = `هل أنت متأكد من ${actionName} ${checkedCount} مستخدم؟\n\nهذا الإجراء سيؤثر على المستخدمين المحددين.`;

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return;
            }

            // Show loading state
            bulkActionBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري التطبيق...';
            bulkActionBtn.disabled = true;

            // Disable form elements during submission
            actionSelect.disabled = true;
            userCheckboxes.forEach(checkbox => checkbox.disabled = true);
        });
    }

    // Add hover effects to action buttons
    const actionButtons = document.querySelectorAll('.btn-sm');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });

    // Initialize
    updateSelectedCount();

    // Add smooth transitions and animations
    const style = document.createElement('style');
    style.textContent = `
        .btn-sm {
            transition: all 0.2s ease;
        }
        .form-check-input {
            transition: all 0.2s ease;
        }
        .table tbody tr {
            transition: background-color 0.2s ease;
        }
        .user-avatar {
            transition: transform 0.2s ease;
        }
        .table tbody tr:hover .user-avatar {
            transform: scale(1.05);
        }
        .stats-card {
            animation-delay: 0.1s;
        }
    `;
    document.head.appendChild(style);

    // Smooth animations for cards
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add interactive feedback for action buttons
    document.querySelectorAll('.btn-action').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
});
</script>
{% endblock %}

</div> <!-- Close container-xxl -->
</div> <!-- Close users-page-container -->

<script>
// Force apply enhanced styles after page load
document.addEventListener('DOMContentLoaded', function() {
    // Force main header styles
    const mainHeader = document.querySelector('.main-header');
    if (mainHeader) {
        mainHeader.style.cssText = `
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            color: white !important;
            padding: 2rem 0 !important;
            margin-bottom: 2rem !important;
            border-radius: 0 0 20px 20px !important;
            box-shadow: 0 6px 15px rgba(0,0,0,0.08) !important;
        `;
    }

    // Force stats card styles
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.style.cssText = `
            border: none !important;
            border-radius: 12px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            transition: transform 0.2s ease, box-shadow 0.2s ease !important;
            background: white !important;
            margin-bottom: 1rem !important;
        `;

        // Add hover effect
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 16px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });
    });

    // Force filter section styles
    const filterSection = document.querySelector('.filter-section');
    if (filterSection) {
        filterSection.style.cssText = `
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            padding: 1.5rem !important;
            margin-bottom: 2rem !important;
        `;
    }

    // Force table container styles
    const tableContainer = document.querySelector('.table-container');
    if (tableContainer) {
        tableContainer.style.cssText = `
            background: white !important;
            border-radius: 12px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            overflow: hidden !important;
        `;
    }

    // Force button styles
    const primaryButtons = document.querySelectorAll('.btn-primary');
    primaryButtons.forEach(btn => {
        btn.style.cssText = `
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            border: none !important;
            border-radius: 12px !important;
            color: white !important;
            padding: 0.5rem 1.25rem !important;
            transition: all 0.3s ease !important;
        `;

        btn.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)';
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)';
            this.style.transform = 'translateY(0)';
        });
    });

    // Force table header styles to match user_index.html
    const tableHeaders = document.querySelectorAll('table th');
    tableHeaders.forEach(th => {
        th.style.cssText = `
            background-color: #f8fafc !important;
            border-bottom: 2px solid #e5e7eb !important;
            font-weight: 600 !important;
            color: #374151 !important;
            padding: 1rem !important;
        `;
    });

    // Force table cell styles to match user_index.html
    const tableCells = document.querySelectorAll('table td');
    tableCells.forEach(td => {
        td.style.cssText = `
            padding: 1rem !important;
            vertical-align: middle !important;
            border-bottom: 1px solid #f3f4f6 !important;
        `;
    });

    // Force table row hover styles to match user_index.html
    const tableRows = document.querySelectorAll('table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f9fafb';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'white';
        });
    });



});
</script>

{% endblock %}
