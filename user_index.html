<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المستخدمين - إدارة المستخدمين</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #6b7280;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
        }

        body {
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .stats-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 0.875rem;
        }

        .role-badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .search-box {
            position: relative;
        }

        .search-box .bi-search {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        .search-box input {
            padding-right: 2.5rem;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #f3f4f6;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .btn-action {
            border: none;
            background: none;
            color: #6b7280;
            padding: 0.5rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .btn-action:hover {
            background-color: #f3f4f6;
            color: #374151;
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Role Colors */
        .role-admin { background-color: #fef2f2; color: #dc2626; }
        .role-manager { background-color: #faf5ff; color: #9333ea; }
        .role-developer { background-color: #eff6ff; color: #2563eb; }
        .role-designer { background-color: #f0fdf4; color: #16a34a; }
        .role-analyst { background-color: #fffbeb; color: #d97706; }
        .role-editor { background-color: #fff7ed; color: #ea580c; }
        .role-user { background-color: #f9fafb; color: #6b7280; }

        /* Status Colors */
        .status-active { background-color: #f0fdf4; color: #16a34a; }
        .status-inactive { background-color: #f9fafb; color: #6b7280; }
        .status-suspended { background-color: #fef2f2; color: #dc2626; }

        /* Avatar Colors */
        .avatar-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .avatar-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .avatar-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .avatar-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .avatar-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .avatar-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .avatar-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .avatar-8 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .avatar-9 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }

        @media (max-width: 768px) {
            .main-header {
                padding: 1rem 0;
            }
            
            .table-responsive {
                font-size: 0.875rem;
            }
            
            .stats-card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h2 mb-2">قائمة المستخدمين</h1>
                    <p class="mb-0 opacity-75">إدارة وتتبع جميع المستخدمين في النظام</p>
                </div>
                <div class="col-md-4 text-md-start text-center mt-3 mt-md-0">
                    <button class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">إجمالي المستخدمين</h6>
                                <h2 class="card-title mb-0">9</h2>
                            </div>
                            <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                                <i class="bi bi-people"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">المستخدمون النشطون</h6>
                                <h2 class="card-title mb-0 text-success">7</h2>
                            </div>
                            <div class="stats-icon bg-success bg-opacity-10 text-success">
                                <i class="bi bi-person-check"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">المستخدمون غير النشطون</h6>
                                <h2 class="card-title mb-0 text-secondary">1</h2>
                            </div>
                            <div class="stats-icon bg-secondary bg-opacity-10 text-secondary">
                                <i class="bi bi-person-x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">المستخدمون المعلقون</h6>
                                <h2 class="card-title mb-0 text-danger">1</h2>
                            </div>
                            <div class="stats-icon bg-danger bg-opacity-10 text-danger">
                                <i class="bi bi-person-slash"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filter-section">
            <div class="row g-3">
                <div class="col-lg-6">
                    <div class="search-box">
                        <input type="text" class="form-control form-control-lg" id="searchInput" placeholder="البحث عن المستخدمين...">
                        <i class="bi bi-search"></i>
                    </div>
                </div>
                <div class="col-lg-3">
                    <select class="form-select form-select-lg" id="roleFilter">
                        <option value="">جميع الأدوار</option>
                        <option value="مدير النظام">مدير النظام</option>
                        <option value="مدير">مدير</option>
                        <option value="مطور">مطور</option>
                        <option value="مصمم">مصمم</option>
                        <option value="محلل">محلل</option>
                        <option value="محرر">محرر</option>
                        <option value="مستخدم">مستخدم</option>
                    </select>
                </div>
                <div class="col-lg-3">
                    <select class="form-select form-select-lg" id="statusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="نشط">نشط</option>
                        <option value="غير نشط">غير نشط</option>
                        <option value="معلق">معلق</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="usersTable">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>آخر نشاط</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-name="john_doe" data-email="<EMAIL>" data-role="مطور" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-1 me-3">JO</div>
                                    <div>
                                        <div class="fw-semibold">john_doe</div>
                                        <small class="text-muted">ID: 1</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567890
                                </div>
                            </td>
                            <td><span class="role-badge role-developer">مطور</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-15</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="jane_smith" data-email="<EMAIL>" data-role="مصمم" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-2 me-3">JA</div>
                                    <div>
                                        <div class="fw-semibold">jane_smith</div>
                                        <small class="text-muted">ID: 2</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567891
                                </div>
                            </td>
                            <td><span class="role-badge role-designer">مصمم</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-14</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="manager" data-email="<EMAIL>" data-role="مدير" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-3 me-3">MA</div>
                                    <div>
                                        <div class="fw-semibold">manager</div>
                                        <small class="text-muted">ID: 3</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567892
                                </div>
                            </td>
                            <td><span class="role-badge role-manager">مدير</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-15</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="admin" data-email="<EMAIL>" data-role="مدير النظام" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-4 me-3">AD</div>
                                    <div>
                                        <div class="fw-semibold">admin</div>
                                        <small class="text-muted">ID: 4</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567893
                                </div>
                            </td>
                            <td><span class="role-badge role-admin">مدير النظام</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-15</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="test_user" data-email="<EMAIL>" data-role="مستخدم" data-status="غير نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-5 me-3">TE</div>
                                    <div>
                                        <div class="fw-semibold">test_user</div>
                                        <small class="text-muted">ID: 5</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567894
                                </div>
                            </td>
                            <td><span class="role-badge role-user">مستخدم</span></td>
                            <td>
                                <span class="status-badge status-inactive">
                                    <i class="bi bi-x-circle"></i>
                                    غير نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-10</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="ahmed_hassan" data-email="<EMAIL>" data-role="محلل" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-6 me-3">AH</div>
                                    <div>
                                        <div class="fw-semibold">ahmed_hassan</div>
                                        <small class="text-muted">ID: 6</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567895
                                </div>
                            </td>
                            <td><span class="role-badge role-analyst">محلل</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-14</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="fatima_ali" data-email="<EMAIL>" data-role="مطور" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-7 me-3">FA</div>
                                    <div>
                                        <div class="fw-semibold">fatima_ali</div>
                                        <small class="text-muted">ID: 7</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567896
                                </div>
                            </td>
                            <td><span class="role-badge role-developer">مطور</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-15</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="omar_salem" data-email="<EMAIL>" data-role="مصمم" data-status="معلق">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-8 me-3">OM</div>
                                    <div>
                                        <div class="fw-semibold">omar_salem</div>
                                        <small class="text-muted">ID: 8</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567897
                                </div>
                            </td>
                            <td><span class="role-badge role-designer">مصمم</span></td>
                            <td>
                                <span class="status-badge status-suspended">
                                    <i class="bi bi-exclamation-circle"></i>
                                    معلق
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-12</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr data-name="sara_mahmoud" data-email="<EMAIL>" data-role="محرر" data-status="نشط">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar avatar-9 me-3">SA</div>
                                    <div>
                                        <div class="fw-semibold">sara_mahmoud</div>
                                        <small class="text-muted">ID: 9</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                    <EMAIL>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                    +1234567898
                                </div>
                            </td>
                            <td><span class="role-badge role-editor">محرر</span></td>
                            <td>
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            </td>
                            <td><span class="text-muted">2024-06-15</span></td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn-action" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-pencil me-2"></i>تعديل المستخدم</a></li>
                                        <li><a class="dropdown-item" href="#"><i class="bi bi-envelope me-2"></i>إرسال رسالة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash me-2"></i>حذف المستخدم</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="text-center mt-4 mb-5">
            <p class="text-muted" id="resultsCount">عرض 9 من أصل 9 مستخدم</p>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Search and Filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const roleFilter = document.getElementById('roleFilter');
            const statusFilter = document.getElementById('statusFilter');
            const tableRows = document.querySelectorAll('#usersTable tbody tr');
            const resultsCount = document.getElementById('resultsCount');
            const totalUsers = tableRows.length;

            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedRole = roleFilter.value;
                const selectedStatus = statusFilter.value;
                let visibleCount = 0;

                tableRows.forEach(row => {
                    const name = row.dataset.name.toLowerCase();
                    const email = row.dataset.email.toLowerCase();
                    const role = row.dataset.role;
                    const status = row.dataset.status;

                    const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
                    const matchesRole = !selectedRole || role === selectedRole;
                    const matchesStatus = !selectedStatus || status === selectedStatus;

                    if (matchesSearch && matchesRole && matchesStatus) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                resultsCount.textContent = `عرض ${visibleCount} من أصل ${totalUsers} مستخدم`;
            }

            // Event listeners
            searchInput.addEventListener('input', filterTable);
            roleFilter.addEventListener('change', filterTable);
            statusFilter.addEventListener('change', filterTable);

            // Smooth animations for cards
            const cards = document.querySelectorAll('.stats-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('animate__animated', 'animate__fadeInUp');
            });
        });

        // Add some interactive feedback
        document.querySelectorAll('.btn-action').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>